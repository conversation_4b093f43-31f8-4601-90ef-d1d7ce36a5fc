package com.cmpay.hacp.inspection.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 按次巡检报告DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "按次巡检报告")
public class PerInspectionReportDTO {

    @ApiModelProperty(value = "报告ID")
    private String reportId;

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "任务执行ID")
    private Long taskExecutionId;

    @ApiModelProperty(value = "触发方式")
    private String triggerMode;

    @ApiModelProperty(value = "执行状态")
    private String executionStatus;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "执行耗时（毫秒）")
    private Long executionDuration;

    @ApiModelProperty(value = "执行耗时（字符串）")
    private String executionTimeStr;

    @ApiModelProperty(value = "报告生成时间")
    private LocalDateTime generateTime;

    @ApiModelProperty(value = "执行概况")
    private ExecutionSummaryDTO executionSummary;

    @ApiModelProperty(value = "执行结果分布")
    private ExecutionDistributionDTO executionDistribution;

    @ApiModelProperty(value = "规则检查详情")
    private List<RuleCheckDetailDTO> ruleCheckDetails;

    @ApiModelProperty(value = "异常详情")
    private List<ExceptionDetailDTO> exceptionDetails;

    /**
     * 执行概况DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "执行概况")
    public static class ExecutionSummaryDTO {
        @ApiModelProperty(value = "开始时间")
        private LocalDateTime startTime;

        @ApiModelProperty(value = "结束时间")
        private LocalDateTime endTime;

        @ApiModelProperty(value = "执行耗时（秒）")
        private Long duration;

        @ApiModelProperty(value = "执行耗时（字符串）")
        private String durationStr;

        @ApiModelProperty(value = "总规则数")
        private Integer totalRules;

        @ApiModelProperty(value = "成功规则数")
        private Integer successRules;

        @ApiModelProperty(value = "失败规则数")
        private Integer failedRules;

        @ApiModelProperty(value = "通过率")
        private Integer passRate;
    }

    /**
     * 执行结果分布DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "执行结果分布")
    public static class ExecutionDistributionDTO {
        @ApiModelProperty(value = "总数")
        private Integer total;

        @ApiModelProperty(value = "成功率")
        private Integer successRate;

        @ApiModelProperty(value = "状态统计")
        private List<StatusCountDTO> statusCounts;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @ApiModel(description = "状态统计")
        public static class StatusCountDTO {
            @ApiModelProperty(value = "状态名称")
            private String status;

            @ApiModelProperty(value = "状态编码")
            private String statusCode;

            @ApiModelProperty(value = "数量")
            private Integer count;

            @ApiModelProperty(value = "百分比")
            private Double percentage;
        }
    }

    /**
     * 规则检查详情DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "规则检查详情")
    public static class RuleCheckDetailDTO {
        @ApiModelProperty(value = "规则ID")
        private String ruleId;

        @ApiModelProperty(value = "规则名称")
        private String ruleName;

        @ApiModelProperty(value = "规则描述")
        private String ruleDescription;

        @ApiModelProperty(value = "检查状态")
        private String checkStatus;

        @ApiModelProperty(value = "检查结果")
        private String checkResult;

        @ApiModelProperty(value = "资源名称")
        private String resourceName;

        @ApiModelProperty(value = "资源类型")
        private String resourceType;

        @ApiModelProperty(value = "执行时间")
        private LocalDateTime executionTime;

        @ApiModelProperty(value = "执行耗时（毫秒）")
        private Long executionDuration;

        @ApiModelProperty(value = "错误信息")
        private String errorMessage;

        @ApiModelProperty(value = "详细信息")
        private String details;
    }

    /**
     * 异常详情DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "异常详情")
    public static class ExceptionDetailDTO {
        @ApiModelProperty(value = "规则ID")
        private String ruleId;

        @ApiModelProperty(value = "规则名称")
        private String ruleName;

        @ApiModelProperty(value = "异常描述")
        private String description;

        @ApiModelProperty(value = "资源名称")
        private String resourceName;

        @ApiModelProperty(value = "异常级别")
        private String level;

        @ApiModelProperty(value = "异常类型")
        private String exceptionType;

        @ApiModelProperty(value = "发生时间")
        private LocalDateTime occurTime;

        @ApiModelProperty(value = "建议措施")
        private String suggestion;

        @ApiModelProperty(value = "详细信息")
        private String details;
    }
}
