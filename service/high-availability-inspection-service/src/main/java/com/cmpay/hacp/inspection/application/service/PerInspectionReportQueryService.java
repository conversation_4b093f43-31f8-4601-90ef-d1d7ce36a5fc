package com.cmpay.hacp.inspection.application.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.ReportDO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 按次巡检报告查询服务接口
 * 负责单次巡检任务报告的查询、统计和展示
 * 
 * 职责分离：只负责报告查询，不负责生成
 */
public interface PerInspectionReportQueryService {

    /**
     * 根据任务ID获取巡检报告
     *
     * @param taskId 任务ID
     * @return 巡检报告，如果不存在则返回null
     */
    ReportDO getReportByTaskId(String taskId);

    /**
     * 根据任务执行ID获取巡检报告
     *
     * @param taskExecutionId 任务执行ID
     * @return 巡检报告，如果不存在则返回null
     */
    ReportDO getReportByTaskExecutionId(Long taskExecutionId);

    /**
     * 根据报告ID获取巡检报告
     *
     * @param reportId 报告ID
     * @return 巡检报告，如果不存在则返回null
     */
    ReportDO getReportByReportId(String reportId);

    /**
     * 分页查询按次巡检报告
     *
     * @param page 分页参数
     * @param queryCondition 查询条件
     * @return 分页结果
     */
    IPage<ReportDO> getReportPage(IPage<?> page, ReportQueryCondition queryCondition);

    /**
     * 获取指定日期的所有按次报告
     * 用于按日汇总报告的数据源
     *
     * @param date 日期
     * @return 按次报告列表
     */
    List<ReportDO> getPerInspectionReportsByDate(LocalDate date);

    /**
     * 获取指定日期范围内的按次报告
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 按次报告列表
     */
    List<ReportDO> getPerInspectionReportsByDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * 获取指定时间范围内的按次报告
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 按次报告列表
     */
    List<ReportDO> getPerInspectionReportsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据任务ID列表获取报告
     *
     * @param taskIds 任务ID列表
     * @return 报告列表
     */
    List<ReportDO> getReportsByTaskIds(List<String> taskIds);

    /**
     * 获取报告统计信息
     *
     * @param queryCondition 查询条件
     * @return 统计信息
     */
    ReportStatistics getReportStatistics(ReportQueryCondition queryCondition);

    /**
     * 检查报告是否存在
     *
     * @param taskId 任务ID
     * @return 是否存在
     */
    boolean isReportExists(String taskId);

    /**
     * 检查报告是否存在（根据任务执行ID）
     *
     * @param taskExecutionId 任务执行ID
     * @return 是否存在
     */
    boolean isReportExistsByExecutionId(Long taskExecutionId);

    /**
     * 获取最新的N个报告
     *
     * @param limit 数量限制
     * @return 报告列表
     */
    List<ReportDO> getLatestReports(int limit);

    /**
     * 获取失败的报告列表
     *
     * @param limit 数量限制
     * @return 失败的报告列表
     */
    List<ReportDO> getFailedReports(int limit);

    /**
     * 导出报告数据
     *
     * @param queryCondition 查询条件
     * @param format 导出格式
     * @return 导出结果
     */
    Map<String, Object> exportReports(ReportQueryCondition queryCondition, String format);

    /**
     * 报告查询条件
     */
    class ReportQueryCondition {
        private String taskId;
        private String taskName;
        private String reportId;
        private LocalDate startDate;
        private LocalDate endDate;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private String executionStatus;
        private String triggerMode;
        private List<String> taskIds;
        
        // getters and setters
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }
        
        public String getTaskName() { return taskName; }
        public void setTaskName(String taskName) { this.taskName = taskName; }
        
        public String getReportId() { return reportId; }
        public void setReportId(String reportId) { this.reportId = reportId; }
        
        public LocalDate getStartDate() { return startDate; }
        public void setStartDate(LocalDate startDate) { this.startDate = startDate; }
        
        public LocalDate getEndDate() { return endDate; }
        public void setEndDate(LocalDate endDate) { this.endDate = endDate; }
        
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        
        public String getExecutionStatus() { return executionStatus; }
        public void setExecutionStatus(String executionStatus) { this.executionStatus = executionStatus; }
        
        public String getTriggerMode() { return triggerMode; }
        public void setTriggerMode(String triggerMode) { this.triggerMode = triggerMode; }
        
        public List<String> getTaskIds() { return taskIds; }
        public void setTaskIds(List<String> taskIds) { this.taskIds = taskIds; }
    }

    /**
     * 报告统计信息
     */
    class ReportStatistics {
        private Integer totalReports;
        private Integer successReports;
        private Integer failedReports;
        private Integer warningReports;
        private Double successRate;
        private Integer totalChecks;
        private Integer successChecks;
        private Integer failedChecks;
        private Double overallPassRate;
        
        // getters and setters
        public Integer getTotalReports() { return totalReports; }
        public void setTotalReports(Integer totalReports) { this.totalReports = totalReports; }
        
        public Integer getSuccessReports() { return successReports; }
        public void setSuccessReports(Integer successReports) { this.successReports = successReports; }
        
        public Integer getFailedReports() { return failedReports; }
        public void setFailedReports(Integer failedReports) { this.failedReports = failedReports; }
        
        public Integer getWarningReports() { return warningReports; }
        public void setWarningReports(Integer warningReports) { this.warningReports = warningReports; }
        
        public Double getSuccessRate() { return successRate; }
        public void setSuccessRate(Double successRate) { this.successRate = successRate; }
        
        public Integer getTotalChecks() { return totalChecks; }
        public void setTotalChecks(Integer totalChecks) { this.totalChecks = totalChecks; }
        
        public Integer getSuccessChecks() { return successChecks; }
        public void setSuccessChecks(Integer successChecks) { this.successChecks = successChecks; }
        
        public Integer getFailedChecks() { return failedChecks; }
        public void setFailedChecks(Integer failedChecks) { this.failedChecks = failedChecks; }
        
        public Double getOverallPassRate() { return overallPassRate; }
        public void setOverallPassRate(Double overallPassRate) { this.overallPassRate = overallPassRate; }
    }
}
