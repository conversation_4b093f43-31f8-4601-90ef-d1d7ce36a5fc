package com.cmpay.hacp.inspection.application.service;

import com.cmpay.hacp.inspection.infrastructure.database.dataobject.ReportDO;

/**
 * 按次巡检报告生成服务接口
 * 负责单次巡检任务报告的生成、重新生成和管理
 * 
 * 职责分离：只负责报告生成，不负责查询
 */
public interface PerInspectionReportGenerationService {

    /**
     * 异步生成按次巡检报告
     * 在任务执行完成后调用，生成详细的巡检报告
     *
     * @param taskId 任务ID
     * @return 任务ID（用于跟踪异步任务）
     */
    String generatePerInspectionReportAsync(String taskId);

    /**
     * 同步生成按次巡检报告
     * 立即生成并返回报告对象
     *
     * @param taskId 任务ID
     * @return 生成的报告对象
     */
    ReportDO generatePerInspectionReport(String taskId);

    /**
     * 根据任务执行ID生成报告
     * 更精确的生成方式，避免任务ID重复执行的问题
     *
     * @param taskExecutionId 任务执行ID
     * @return 生成的报告对象
     */
    ReportDO generatePerInspectionReportByExecutionId(Long taskExecutionId);

    /**
     * 重新生成按次巡检报告
     * 用于修复有问题的报告或更新报告内容
     *
     * @param taskId 任务ID
     * @param force 是否强制重新生成（忽略已存在的报告）
     * @return 重新生成的报告对象
     */
    ReportDO regeneratePerInspectionReport(String taskId, boolean force);

    /**
     * 重新生成指定报告ID的报告
     *
     * @param reportId 报告ID
     * @param force 是否强制重新生成
     * @return 重新生成的报告对象
     */
    ReportDO regeneratePerInspectionReportByReportId(String reportId, boolean force);

    /**
     * 批量生成按次巡检报告
     * 用于批量处理多个任务的报告生成
     *
     * @param taskIds 任务ID列表
     * @return 生成成功的报告数量
     */
    int batchGeneratePerInspectionReports(java.util.List<String> taskIds);

    /**
     * 删除按次巡检报告
     * 根据任务ID删除相关报告
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    boolean deleteReportByTaskId(String taskId);

    /**
     * 删除指定报告ID的报告
     *
     * @param reportId 报告ID
     * @return 是否删除成功
     */
    boolean deleteReportByReportId(String reportId);

    /**
     * 验证报告数据的完整性和准确性
     *
     * @param reportId 报告ID
     * @return 验证结果
     */
    ValidationResult validateReport(String reportId);

    /**
     * 获取报告生成进度
     * 用于异步生成任务的进度跟踪
     *
     * @param taskId 任务ID
     * @return 生成进度信息
     */
    GenerationProgress getGenerationProgress(String taskId);

    /**
     * 取消正在进行的报告生成任务
     *
     * @param taskId 任务ID
     * @return 是否取消成功
     */
    boolean cancelGenerationTask(String taskId);

    /**
     * 报告生成进度信息
     */
    class GenerationProgress {
        private String taskId;
        private String status; // PENDING, RUNNING, COMPLETED, FAILED
        private Integer progressPercentage;
        private String currentStep;
        private String errorMessage;
        private java.time.LocalDateTime startTime;
        private java.time.LocalDateTime endTime;
        
        // getters and setters
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public Integer getProgressPercentage() { return progressPercentage; }
        public void setProgressPercentage(Integer progressPercentage) { this.progressPercentage = progressPercentage; }
        
        public String getCurrentStep() { return currentStep; }
        public void setCurrentStep(String currentStep) { this.currentStep = currentStep; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public java.time.LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(java.time.LocalDateTime startTime) { this.startTime = startTime; }
        
        public java.time.LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(java.time.LocalDateTime endTime) { this.endTime = endTime; }
    }

    /**
     * 验证结果
     */
    class ValidationResult {
        private boolean valid;
        private java.util.List<String> issues;
        private String summary;
        
        // getters and setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public java.util.List<String> getIssues() { return issues; }
        public void setIssues(java.util.List<String> issues) { this.issues = issues; }
        
        public String getSummary() { return summary; }
        public void setSummary(String summary) { this.summary = summary; }
    }
}
